import sys
import random 
from enum import Enum 

game_count = 0

def play_rps():

    class RPS(Enum):
        ROCK = 1
        PAPER = 2
        SCISSORS = 3

    playerchoice = input("\nEnter.... \n 1 for ROCK \n 2 for PAPER \n 3 for SCISSORS \n")

    if playerchoice not in ["1", "2", "3"]:
        print("You must enter 1,2 or 3.")
        return play_rps()
    
    player = int(playerchoice)

    computerchoice = random.choice("123")

    computer = int(computerchoice)


    print("\nYou choose " + playerchoice + ".")
    print("Python chose " + computerchoice + ".\n")

    def decide_winner(player, computer):
        if player == 1 and computer == 3:
            return "You win!"
        elif player == 2 and computer == 1:
            return "You win!"
        elif player == 3 and computer == 2:
            return "You win!"
        elif player == computer:
            return "Draw!"
        else:
            return "You lose!"
        
    game_result = decide_winner(player, computer)
    
    print(game_result)

    global game_count
    game_count += 1

    print("\nGame count : " + str(game_count))

    print("\nPlay again?")

    while True:
        playagain = input("\nY for yes or \nQ for Quit \n")
        if playagain.lower() not in ["y", "q"]:
            continue 
        else:
            break

    if playagain.lower() == "y":
        return play_rps()
    else:
        print("\nGoodbye!")
        print('Thank you for playing!\n')
        sys.exit('Bye!')
        #break

play_rps()

if __name__ == "__main__":
    play_rps()