import re
import sys

try:
    with open("story.txt", 'r') as f:
        story = f.read()
except FileNotFoundError:
    print("Error: 'story.txt' not found. Please create it in the same directory as the script.")
    sys.exit(1)

# Use a regular expression to find all unique placeholders (e.g., <noun>)
words = set(re.findall(r"<[^>]+>", story))

if not words:
    print("Warning: No placeholders like '<word>' found in 'story.txt'.")
    print("The story is:\n")
    print(story)
    sys.exit(0)

answers = {}
for word in words:
    # Create a more user-friendly prompt by removing the angle brackets
    prompt_text = word[1:-1]
    answer = input(f"Enter a {prompt_text}: ")
    answers[word] = answer

# Replace each placeholder in the story with the user's answer
for word, answer in answers.items():
    story = story.replace(word, answer)

print("\n--- Your Mad Libs Story ---")
print(story)
