from typing import List, Tuple
import math

class BubbleLayout:
    @staticmethod
    def calculate_bubble_size(content: str) -> <PERSON>ple[int, int]:
        # Calculate appropriate bubble size based on content
        min_width = 100
        min_height = 60
        chars_per_line = 20
        lines = math.ceil(len(content) / chars_per_line)
        return (max(min_width, len(content) * 7), max(min_height, lines * 20))
    
    @staticmethod
    def calculate_connection_points(start_pos: <PERSON><PERSON>[float, float], 
                                 end_pos: <PERSON>ple[float, float]) -> <PERSON><PERSON>[Tuple[float, float], <PERSON><PERSON>[float, float]]:
        # Calculate optimal connection points between bubbles
        return start_pos, end_pos