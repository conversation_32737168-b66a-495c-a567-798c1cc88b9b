{"cells": [{"cell_type": "code", "execution_count": 4, "id": "4e9537ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", "[[0.7 0.7 0.7 0.7 0.7 0.7 0.7 0.7 0.7 0.7]\n", " [0.7 0.7 0.7 0.7 0.7 0.7 0.7 0.7 0.7 0.7]]\n", "[ 0.          4.16666667  8.33333333 12.5        16.66666667 20.83333333\n", " 25.        ]\n", "<class 'numpy.ndarray'>\n"]}], "source": ["import numpy as np\n", "\n", "a = np.zeros(10)\n", "print(a)\n", "\n", "b = np.full((2,10), 0.7)\n", "print(b)\n", "\n", "c = np.linspace(0,25,7)\n", "print(c)\n", "\n", "print(type(c))"]}, {"cell_type": "code", "execution_count": null, "id": "0f47c019", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "d1bb9f39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Animals   Sounds\n", "0       Dog    Barks\n", "1       Cat     Meow\n", "2      Lion    Roars\n", "3       Cow      Moo\n", "4  Elephant  Trumpet\n", "       Animals Sounds\n", "count        5      5\n", "unique       5      5\n", "top        Dog  Barks\n", "freq         1      1\n", "  Letters  Numbers\n", "5       f        1\n", "3       d        3\n", "4       e        5\n", "1       b        7\n", "2       c        9\n", "0       a       12\n", "  Letters  Numbers  new_values\n", "0       a       12          36\n", "1       b        7          21\n", "2       c        9          27\n", "3       d        3           9\n", "4       e        5          15\n", "5       f        1           3\n"]}], "source": ["import pandas as pd\n", "\n", "a = pd.DataFrame({'Animals': ['<PERSON>','<PERSON>','<PERSON>','Cow','Elephant'],\n", "                    'Sounds':['<PERSON><PERSON>','Me<PERSON>','Roars','<PERSON><PERSON>','<PERSON>et']})\n", "\n", "print(a)\n", "print(a.describe())\n", "\n", "b = pd.DataFrame({\n", "    \"Letters\" : ['a', 'b', 'c', 'd', 'e', 'f'],\n", "    \"Numbers\" : [12, 7, 9, 3, 5, 1]  })\n", "\n", "print(b.sort_values(by=\"Numbers\"))\n", "\n", "b = b.assign(new_values = b['Numbers']*3)\n", "print(b)\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "ab6f5494", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'numpy' has no attribute 'int'.\n`np.int` was a deprecated alias for the builtin `int`. To avoid this error in existing code, use `int` by itself. Doing this will not modify any behavior and is safe. When replacing `np.int`, you may wish to use e.g. `np.int64` or `np.int32` to specify the precision. If you wish to review your current use, check the release note link for additional information.\nThe aliases was originally deprecated in NumPy 1.20; for more details and guidance see the original release note at:\n    https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m X = np.arange(\u001b[32m4\u001b[39m, dtype=\u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mint\u001b[49m)\n\u001b[32m      3\u001b[39m np.ones_like(X)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\__init__.py:410\u001b[39m, in \u001b[36m__getattr__\u001b[39m\u001b[34m(attr)\u001b[39m\n\u001b[32m    405\u001b[39m     warnings.warn(\n\u001b[32m    406\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mIn the future `np.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattr\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m` will be defined as the \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    407\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mcorresponding NumPy scalar.\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;167;01mFutureWarning\u001b[39;00m, stacklevel=\u001b[32m2\u001b[39m)\n\u001b[32m    409\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m attr \u001b[38;5;129;01min\u001b[39;00m __former_attrs__:\n\u001b[32m--> \u001b[39m\u001b[32m410\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(__former_attrs__[attr], name=\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    412\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m attr \u001b[38;5;129;01min\u001b[39;00m __expired_attributes__:\n\u001b[32m    413\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\n\u001b[32m    414\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m`np.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattr\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m` was removed in the NumPy 2.0 release. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    415\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m__expired_attributes__[attr]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m,\n\u001b[32m    416\u001b[39m         name=\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    417\u001b[39m     )\n", "\u001b[31mAttributeError\u001b[39m: module 'numpy' has no attribute 'int'.\n`np.int` was a deprecated alias for the builtin `int`. To avoid this error in existing code, use `int` by itself. Doing this will not modify any behavior and is safe. When replacing `np.int`, you may wish to use e.g. `np.int64` or `np.int32` to specify the precision. If you wish to review your current use, check the release note link for additional information.\nThe aliases was originally deprecated in NumPy 1.20; for more details and guidance see the original release note at:\n    https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations"]}], "source": ["import numpy as np\n", "X = np.arange(4, dtype=np.int)\n", "np.ones_like(X)"]}, {"cell_type": "code", "execution_count": 6, "id": "2a646ff8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n", "True\n", "False\n"]}], "source": ["a, b, c = False, False, True \n", "\n", "print(not a)\n", "print(not c)\n", "print(not(a and b))\n", "print(not (c or c))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}