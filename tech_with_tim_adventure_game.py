name = input('Type your name: ').lower()
print(f"Hello {name}, welcome to the adventure game!")

answer = input("You are on a dirt road, it has come to an end and you can either choose to go left or right. Which way wil you go? ").lower()

if answer == "left":
    answer = input('You come to a river, you can walk around it or swim across it? Type walk to walk or swim to swim across: ')
    
    if answer == 'walk':
        print("You swim across and you were eaten by an alligator")
    elif answer == "swim":
        print("You walked for many miles, ran out of water and you lost the game")
    else:
        print("You didn't answer correctly, please try again.")

elif answer == 'right':
    answer = input("You come to a bridge, it looks wobbly, do you want to cross it or head back (cross/back)?  ")

    if answer == 'back':
        print("You go back and you will lose")
    elif answer == "cross":
        answer = input("You cross the bridge and meet a stranger. Do you talk to them (yes/no)? ")
    
        if answer == "yes":
            print("You talk to the stranger, they give you gold, YOU WIN!")
        elif answer == "no":
            print("You ignore the stanger, they get offended and YOU LOSE!")
        else:
            print("You didn't answer correctly, please try again.")
    
    else:
        print("You didn't answer correctly, please try again.")

else:
    print("You didn't answer correctly, please try again.")

print(f'Thank you for playing, {name}! ')
quit()