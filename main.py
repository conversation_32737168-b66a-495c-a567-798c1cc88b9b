# my_pets = ['alfred', 'tabitha', 'william', 'arla']
# uppered_pets = []

# for pet in my_pets:
#     pet_ = pet.upper()
#     uppered_pets.append(pet_)

# print(uppered_pets)

# my_pets = ['alfred', 'tabitha', 'william', 'arla']
# uppered_pets = list(map(str.upper, my_pets))
# print(uppered_pets)

def fib():
    a, b = 1,1
    while 1:
        yield a, b = b, a + b

import types

if types(fib()) == types.GeneratorType:
    print("It's a generator")

    counter = 0
    for n in fib():
        print(n)
        