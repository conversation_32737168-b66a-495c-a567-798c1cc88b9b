{"cells": [{"cell_type": "markdown", "id": "3a31e40c", "metadata": {}, "source": ["#### mathematics \n"]}, {"cell_type": "code", "execution_count": null, "id": "b12efec3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 1, "id": "64998ede", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "7c488262", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "-1\n"]}], "source": ["x = 1\n", "y = 2\n", "\n", "def add(x, y):\n", "    return x + y\n", "print(add(x, y))\n", "\n", "def sub(x, y):\n", "    return x - y\n", "print(sub(x, y))"]}, {"cell_type": "code", "execution_count": 3, "id": "fce76f51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[['a' 'b']\n", " ['d' 'e']\n", " ['g' 'h']]\n"]}], "source": ["A = np.array([\n", "    ['a', 'b', 'c'],\n", "    ['d', 'e', 'f'],\n", "    ['g', 'h', 'i']\n", "])\n", "\n", "print(A[:, :2])"]}, {"cell_type": "code", "execution_count": 4, "id": "619850e3", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([20, 21, 22, 23, 24])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.arange(5)\n", "a + 20"]}, {"cell_type": "code", "execution_count": 5, "id": "1affe2ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>\n", "Canine\n"]}], "source": ["class Dog:\n", "    species = 'Canine'\n", "\n", "    def __init__(self, name, age):\n", "        self.name = name\n", "        self.age = age\n", "\n", "dog1 = <PERSON>('Buddy', 3)\n", "print(dog1.name)\n", "print(dog1.species)"]}, {"cell_type": "code", "execution_count": 5, "id": "f140eb01", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 2, 3])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "a = np.arange(4)\n", "a"]}, {"cell_type": "code", "execution_count": 6, "id": "450bf069", "metadata": {}, "outputs": [{"data": {"text/plain": ["(np.int64(0), np.int64(3))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["a[0], a[-1]"]}, {"cell_type": "code", "execution_count": 7, "id": "efed83c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 3])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["a[[0, -1]]"]}, {"cell_type": "code", "execution_count": 8, "id": "10c7932b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 3])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["a[[True, False, False, True]]"]}, {"cell_type": "code", "execution_count": 9, "id": "dabac55a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 2, 3])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 10, "id": "ee7a6018", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([False, False,  True,  True])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["a >= 2"]}, {"cell_type": "code", "execution_count": 11, "id": "cd1e9428", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2, 3])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a >= 2]"]}, {"cell_type": "code", "execution_count": 13, "id": "ebca4429", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.5)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["a.mean()"]}, {"cell_type": "code", "execution_count": 14, "id": "85f99bbe", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2, 3])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a > a.mean()]"]}, {"cell_type": "code", "execution_count": 15, "id": "7db30065", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["a[~(a > a.mean())]"]}, {"cell_type": "code", "execution_count": 16, "id": "95aa2f77", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["a[(a == 0) | (a == 1)]"]}, {"cell_type": "code", "execution_count": 17, "id": "d9b0cbe4", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 2])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["a[(a <= 2) & (a % 2 == 0)]"]}, {"cell_type": "code", "execution_count": 19, "id": "5d7cc6ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[42, 33, 29],\n", "       [51,  4,  8],\n", "       [20, 75, 67]], dtype=int32)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["A = np.random.randint(100, size=(3, 3))\n", "A"]}, {"cell_type": "code", "execution_count": 20, "id": "37a2199f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([42, 29,  4, 20, 67], dtype=int32)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["A[np.array([\n", "    [True, False, True],\n", "    [False, True, False],\n", "    [True, False, True]\n", "])]"]}, {"cell_type": "code", "execution_count": 21, "id": "ff80a9df", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ True,  True, False],\n", "       [ True, False, False],\n", "       [False,  True,  True]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["A > 30"]}, {"cell_type": "code", "execution_count": 22, "id": "bb707f64", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([42, 33, 51, 75, 67], dtype=int32)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["A[A > 30]"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}