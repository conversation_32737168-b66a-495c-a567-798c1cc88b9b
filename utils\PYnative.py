# n = int(input('Enter number '))
# sum = 0 

# for num in range(1, n + 1, 1):
#     sum = sum + num
# print("Sum of first ", n, "numbers is: ", sum)
# average = sum / n
# print("Average of ", n, 'numbers is: ', average)

# n = 10
# res = sum(range(1, n + 1))
# print("Sum of first ", n, "number is: ", res)

num_list = [10,20.5,30,45.5,50]

res = sum(num_list)
avg = res / len(num_list)
print("sum is: ", res, "Average is: ", avg)

res1 = 0
for num in num_list:
    res1 += num
avg1 = res1 / len(num_list)
print("sum is: ", res1, "Average is: ", avg1)